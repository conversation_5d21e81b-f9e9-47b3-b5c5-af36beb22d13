using System;
using System.Globalization;
using System.Linq;

namespace ShiningCMusicCommon.Utilities
{
    /// <summary>
    /// Utility class for fixing COUNT-based recurrence rules when editing following series.
    /// Splits a COUNT-based recurrence into two COUNT-based rules.
    /// </summary>
    public static class RecurrenceFixer
    {
        /// <summary>
        /// Splits a COUNT-based recurrence into two COUNT-based rules.
        /// Returns (firstSeriesRule, secondSeriesRule).
        /// </summary>
        public static (string firstRule, string secondRule) SplitAsCount(
            string originalRule,
            DateTime splitStart,
            DateTime originalStart)
        {
            if (string.IsNullOrWhiteSpace(originalRule) || !originalRule.Contains("COUNT", StringComparison.OrdinalIgnoreCase))
                throw new ArgumentException("Rule must be COUNT-based.", nameof(originalRule));

            // Parse original rule
            var parts = originalRule
                .Split(';', StringSplitOptions.RemoveEmptyEntries) // remove empty strings
                .Select(p => p.Split('=', 2))                     // split into 2 pieces max
                .Where(a => a.Length == 2)                        // only keep valid key=value
                .ToDictionary(a => a[0].ToUpperInvariant(), a => a[1]);

            var freq = parts["FREQ"];
            var byDay = parts.TryGetValue("BYDAY", out var byDayValue) ? byDayValue : null;
            var interval = parts.TryGetValue("INTERVAL", out var intervalValue) ? int.Parse(intervalValue) : 1;
            var totalCount = int.Parse(parts["COUNT"]);

            // Generate all occurrences from the original start
            var allOccurrences = GenerateOccurrences(originalStart, freq, byDay, interval, totalCount);

            // Count how many are before the split date
            var firstHalfCount = allOccurrences.Count(d => d < splitStart);
            var secondHalfCount = totalCount - firstHalfCount;

            if (firstHalfCount < 1 || secondHalfCount < 1)
                throw new InvalidOperationException("Split point results in empty series.");

            // Build first rule
            var firstRuleParts = parts.ToDictionary(kv => kv.Key, kv => kv.Value);
            firstRuleParts["COUNT"] = firstHalfCount.ToString();
            var firstRule = string.Join(";", firstRuleParts.Select(kv => kv.Key + "=" + kv.Value));

            // Build second rule
            var secondRuleParts = parts.ToDictionary(kv => kv.Key, kv => kv.Value);
            secondRuleParts["COUNT"] = secondHalfCount.ToString();
            var secondRule = string.Join(";", secondRuleParts.Select(kv => kv.Key + "=" + kv.Value));

            return (firstRule, secondRule);
        }

        private static DateTime[] GenerateOccurrences(DateTime start, string freq, string? byDay, int interval, int count)
        {
            var dates = new DateTime[count];
            var current = start;
            var byDays = string.IsNullOrEmpty(byDay) ? null : byDay.Split(',');

            for (int i = 0; i < count; i++)
            {
                dates[i] = current;

                if (freq == "WEEKLY")
                {
                    if (byDays != null && byDays.Length > 0)
                    {
                        // Multi-day weekly recurrence
                        var dayCodes = byDays.Select(ParseDay).ToArray();
                        // Move to next matching day
                        DateTime next;
                        do
                        {
                            current = current.AddDays(1);
                            next = current;
                        } while (!dayCodes.Contains(next.DayOfWeek));
                    }
                    else
                    {
                        current = current.AddDays(7 * interval);
                    }
                }
                else if (freq == "DAILY")
                {
                    current = current.AddDays(interval);
                }
                else
                {
                    throw new NotSupportedException($"Frequency {freq} not implemented");
                }
            }

            return dates;
        }

        private static DayOfWeek ParseDay(string dayCode) =>
            dayCode switch
            {
                "MO" => DayOfWeek.Monday,
                "TU" => DayOfWeek.Tuesday,
                "WE" => DayOfWeek.Wednesday,
                "TH" => DayOfWeek.Thursday,
                "FR" => DayOfWeek.Friday,
                "SA" => DayOfWeek.Saturday,
                "SU" => DayOfWeek.Sunday,
                _ => throw new ArgumentException($"Unknown BYDAY code: {dayCode}")
            };
    }
}
