# RecurrenceFixer - COUNT-Based Recurrence Rule Splitting

## Overview

The `RecurrenceFixer` class solves a specific problem with COUNT-based recurring events when editing "following events" in a series. When Syncfusion's scheduler edits following events in a COUNT-based series, it doesn't properly split the COUNT value between the original and new series, leading to incorrect occurrence counts.

## The Problem

**Before Fix:**
- Original series: `FREQ=WEEKLY;COUNT=10` (10 total lessons)
- User edits occurrence #5 and following → "Edit Following Events"
- Result: Both series have `COUNT=10` → Total: 20 lessons (incorrect!)

**After Fix:**
- Original series: `FREQ=WEEKLY;COUNT=4` (lessons 1-4)
- New series: `FREQ=WEEKLY;COUNT=6` (lessons 5-10)
- Result: Correct total of 10 lessons

## Usage

### From Frontend (Blazor) - ✅ **IMPLEMENTED**

The RecurrenceFixer is now **automatically integrated** into the Lessons page `OnActionBegin` method:

```csharp
// This code is already implemented in ShiningCMusicApp/Pages/Lessons.razor.cs
protected async Task OnActionBegin(ActionEventArgs<ScheduleEvent> args)
{
    // ... permission checks ...

    // Fix COUNT-based recurrence rules when editing following events
    if (args.RequestType == "eventChange" && args.AddedRecords?.Count > 0 && args.ChangedRecords?.Count > 0)
    {
        var originalEvent = args.ChangedRecords.First();
        var newEvent = args.AddedRecords.First();

        if (!string.IsNullOrEmpty(originalEvent.RecurrenceRule) &&
            originalEvent.RecurrenceRule.Contains("COUNT", StringComparison.OrdinalIgnoreCase))
        {
            try
            {
                var (firstRule, secondRule) = RecurrenceFixer.SplitAsCount(
                    originalRule: originalEvent.RecurrenceRule,
                    splitStart: newEvent.StartTime,
                    originalStart: originalEvent.StartTime);

                // Fix both series rules
                originalEvent.RecurrenceRule = firstRule;
                newEvent.RecurrenceRule = secondRule;

                await JSRuntime.InvokeVoidAsync("console.log",
                    $"Fixed COUNT recurrence - Original: {firstRule}, New: {secondRule}");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.warn",
                    $"Could not fix COUNT recurrence: {ex.Message}");
                // Continue without fixing - let the original logic handle it
            }
        }
    }
}
```

**✅ No additional frontend code needed - it works automatically!**

### From API Controller

```csharp
[HttpPost("fix-count-recurrence")]
public IActionResult FixCountRecurrence([FromBody] FixRecurrenceRequest request)
{
    try
    {
        var (firstRule, secondRule) = _lessonService.FixCountBasedRecurrence(
            request.OriginalRule,
            request.SplitStart,
            request.OriginalStart);

        return Ok(new { FirstRule = firstRule, SecondRule = secondRule });
    }
    catch (Exception ex)
    {
        return BadRequest(ex.Message);
    }
}
```

### Direct Usage

```csharp
using ShiningCMusicCommon.Utilities;

// Example: Split a 10-lesson weekly series at the 5th occurrence
var originalRule = "FREQ=WEEKLY;INTERVAL=1;COUNT=10";
var originalStart = new DateTime(2024, 1, 1, 10, 0, 0);
var splitStart = new DateTime(2024, 1, 29, 10, 0, 0); // 5th occurrence

var (firstRule, secondRule) = RecurrenceFixer.SplitAsCount(
    originalRule, splitStart, originalStart);

// Result:
// firstRule = "FREQ=WEEKLY;INTERVAL=1;COUNT=4"
// secondRule = "FREQ=WEEKLY;INTERVAL=1;COUNT=6"
```

**✅ Available from any project that references `ShiningCMusicCommon`**

## Supported Recurrence Patterns

- ✅ **DAILY**: `FREQ=DAILY;COUNT=10`
- ✅ **WEEKLY**: `FREQ=WEEKLY;COUNT=8`
- ✅ **WEEKLY with BYDAY**: `FREQ=WEEKLY;BYDAY=MO,WE,FR;COUNT=15`
- ✅ **WEEKLY with INTERVAL**: `FREQ=WEEKLY;INTERVAL=2;COUNT=6`
- ❌ **MONTHLY/YEARLY**: Not yet implemented
- ❌ **UNTIL-based rules**: Only COUNT-based rules are supported

## Error Handling

The method throws exceptions for invalid inputs:

```csharp
// ArgumentException: Rule must be COUNT-based
RecurrenceFixer.SplitAsCount("FREQ=WEEKLY;UNTIL=20241231T235959Z", ...);

// InvalidOperationException: Split point results in empty series
RecurrenceFixer.SplitAsCount("FREQ=WEEKLY;COUNT=5", futureDate, originalStart);
```

## Integration Architecture - ✅ **FULLY IMPLEMENTED**

The RecurrenceFixer is now directly integrated in the frontend:

```
Frontend (Lessons.razor.cs)
    ↓ OnActionBegin()
    ↓ calls directly
RecurrenceFixer.SplitAsCount() ← ShiningCMusicCommon.Utilities
    ↓
Result: Fixed COUNT rules
```

**📍 Location**: `ShiningCMusicCommon/Utilities/RecurrenceFixer.cs`
**🎯 Usage**: Direct call from frontend - no service layer needed!

### Integration Points

1. ✅ **RecurrenceFixer**: Core utility class in `ShiningCMusicCommon.Utilities`
2. ✅ **Frontend**: Direct integration in `OnActionBegin` event handler

**🚀 Simplified Architecture**: No service layer wrappers needed - frontend calls RecurrenceFixer directly!

## When to Use

Use this fix when:
- ✅ Editing "following events" in a recurring series
- ✅ Original series uses COUNT-based recurrence rules
- ✅ You need accurate lesson counts for billing/scheduling

Don't use when:
- ❌ Series uses UNTIL-based rules (not supported)
- ❌ Editing single occurrences (not needed)
- ❌ Series has no COUNT parameter

## Testing

Test scenarios to verify the fix works:
1. Create a COUNT=10 weekly series
2. Edit the 5th occurrence → "Edit Following Events"
3. Verify original series shows COUNT=4
4. Verify new series shows COUNT=6
5. Confirm total lessons = 10 (not 20)
