namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffEmailService
    {
        Task<bool> SendScheduleReadyEmailAsync(int tutorId);
        Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true);
        Task<bool> SendTemplateEmailAsync(int tutorId, string templateName);
        Task<bool> SendTemplateEmailToStudentAsync(int studentId, string templateName, int? paymentDeadlineDays = null);
    }
}
