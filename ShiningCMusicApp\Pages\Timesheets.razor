@page "/timesheets"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Microsoft.AspNetCore.Authorization
@attribute [RequireLevel20Access]
@inherits TimesheetsBase

<PageTitle>Timesheet Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">📋 <span class="d-none d-sm-inline">Timesheet Management</span><span class="d-sm-none">Timesheets</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading timesheets...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Timesheets</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                @if (CanCreateTimesheets)
                                {
                                    <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateModal">
                                        <i class="bi bi-clipboard-plus" style="color: white;"></i>
                                        <span class="d-none d-sm-inline ms-2">Add New Timesheet</span>
                                        <span class="d-sm-none ms-2">Add Timesheet</span>
                                    </button>
                                }
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfGrid DataSource="@timesheets" AllowPaging="true" AllowSorting="true" AllowFiltering="true" 
                                AllowResizing="true" GridLines="GridLine.Both" Height="900">
                            <GridPageSettings PageSize="20"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(Timesheet.CreatedUTC)" Direction="SortDirection.Descending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Timesheet.Student.StudentName) HeaderText="Student Name" Width="150">
                                    <Template>
                                        @{
                                            var timesheet = (context as Timesheet);
                                            var studentName = timesheet != null ? GetStudentName(timesheet.StudentId) : "Unknown";
                                        }
                                        @studentName
                                    </Template>
                                </GridColumn>
                                @if (CanEditTimesheets)
                                {
                                    <GridColumn Field=@nameof(Timesheet.Tutor.TutorName) HeaderText="Tutor Name" Width="150">
                                        <Template>
                                            @{
                                                var timesheet = (context as Timesheet);
                                                var tutorName = timesheet != null ? GetTutorName(timesheet.TutorId) : "Unknown";
                                            }
                                            @tutorName
                                        </Template>
                                    </GridColumn>
                                }
                                <GridColumn Field=@nameof(Timesheet.StartDate) HeaderText="Start Date" Width="120" Format="dd/MM/yyyy" Type="ColumnType.Date"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.ClassDurationMinutes) HeaderText="Duration (min)" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.ContactNumber) HeaderText="Contact" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(Timesheet.CreatedUTC) HeaderText="Created" Width="120" Format="dd/MM/yyyy" Type="ColumnType.Date"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var timesheet = (context as Timesheet);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-success grid-action-btn grid-btn-third" @onclick="() => ViewTimesheet(timesheet)" title="View Details">
                                                <i class="bi bi-eye"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">View Details</span>
                                                }
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary grid-action-btn grid-btn-third" @onclick="() => OpenEditModal(timesheet)" title="Edit" disabled="@(!CanEditTimesheets)">
                                                <i class="bi bi-pencil"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Edit</span>
                                                }
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger grid-action-btn grid-btn-third" @onclick="() => DeleteTimesheet(timesheet)" title="Delete" disabled="@(!CanEditTimesheets)">
                                                <i class="bi bi-trash"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Delete</span>
                                                }
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="600px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTimesheet" OnValidSubmit="@SaveTimesheet">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Student *</label>
                        <SfDropDownList TValue="int" TItem="Student" @bind-Value="currentTimesheet.StudentId"
                                        DataSource="@students" Placeholder="Select Student">
                            <DropDownListFieldSettings Value="StudentId" Text="StudentName"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int" TItem="Student" ValueChange="@OnStudentChanged"></DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Subject *</label>
                        <SfDropDownList TValue="int" TItem="Subject" @bind-Value="currentTimesheet.SubjectId"
                                        DataSource="@subjects" Placeholder="Select Subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Tutor *</label>
                        <SfDropDownList TValue="int" TItem="Tutor" @bind-Value="currentTimesheet.TutorId"
                                        DataSource="@tutors" Placeholder="Select Tutor">
                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Class Duration (minutes) *</label>
                        <SfNumericTextBox TValue="int" @bind-Value="currentTimesheet.ClassDurationMinutes"
                                          Min="15" Max="180" Step="15"></SfNumericTextBox>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Start Date *</label>
                        <SfDatePicker TValue="DateTime" @bind-Value="currentTimesheet.StartDate"
                                      Format="dd/MM/yyyy" Placeholder="Select start date"></SfDatePicker>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Contact Number</label>
                        <SfTextBox @bind-Value="currentTimesheet.ContactNumber" Placeholder="Contact number"></SfTextBox>
                    </div>
                    <div class="col-12">
                        <label class="form-label">Notes</label>
                        <SfTextBox @bind-Value="currentTimesheet.Notes" Multiline="true" Placeholder="Additional notes"></SfTextBox>
                    </div>
                    @if (!isEditMode)
                    {
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Auto-create Attendance Records:</strong> Specify how many blank attendance records to create automatically with this timesheet.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Number of Attendance Records to Create</label>
                            <SfNumericTextBox TValue="int" @bind-Value="numberOfEntriesForNewTimesheet"
                                              Min="0" Max="50" Step="1"
                                              Placeholder="Enter number (0 for none)"></SfNumericTextBox>
                            <div class="form-text">
                                Leave as 0 to create timesheet without any attendance records. You can add them later.
                            </div>
                        </div>
                    }
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- View Details Modal -->
<SfDialog @bind-Visible="showViewModal" Header="@($"Timesheet Details - {GetStudentName(selectedTimesheet?.StudentId)}")" Width="1200px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            @if (selectedTimesheet != null)
            {
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <strong>Student:</strong> @GetStudentName(selectedTimesheet.StudentId)
                    </div>
                    <div class="col-md-4">
                        <strong>Tutor:</strong> @GetTutorName(selectedTimesheet.TutorId)
                    </div>
                    <div class="col-md-4">
                        <strong>Start Date:</strong> @selectedTimesheet.StartDate.ToString("dd/MM/yyyy")
                    </div>
                    <div class="col-md-4">
                        <strong>Duration:</strong> @selectedTimesheet.ClassDurationMinutes minutes
                    </div>
                    <div class="col-md-6">
                        <strong>Contact:</strong> @selectedTimesheet.ContactNumber
                    </div>
                    <div class="col-md-6">
                        <strong>Created:</strong> @selectedTimesheet.CreatedUTC.ToString("dd/MM/yyyy")
                    </div>
                    @if (!string.IsNullOrEmpty(selectedTimesheet.Notes))
                    {
                        <div class="col-12">
                            <strong>Notes:</strong> @selectedTimesheet.Notes
                        </div>
                    }
                </div>

                <h6>Attendance Records</h6>
                <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end mb-3">
                    @if (CanCreateTimesheets)
                    {
                        <button class="btn btn-outline-primary" style="min-width: 160px;" @onclick="OpenAddEntryModal">
                            <i class="bi bi-clipboard-plus-fill" style="color: inherit;"></i>
                            <span class="ms-2">Bulk Create</span>
                        </button>
                        <button class="btn btn-primary" style="min-width: 160px;" @onclick="OpenSingleEntryModal">
                            <i class="bi bi-clipboard-plus" style="color: inherit;"></i>
                            <span class="ms-2">Add Single</span>
                        </button>
                    }
                    <button class="btn btn-success" style="min-width: 160px;" @onclick="ExportTimesheetToExcel">
                        <i class="bi bi-file-earmark-excel" style="color: inherit;"></i>
                        <span class="ms-2">Export to Excel</span>
                    </button>
                </div>

                <SfGrid DataSource="@timesheetEntries" AllowPaging="true" AllowSorting="true" Height="300">
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridSortSettings>
                        <GridSortColumns>
                            <GridSortColumn Field="@nameof(TimesheetEntry.RecordId)" Direction="SortDirection.Ascending"></GridSortColumn>
                        </GridSortColumns>
                    </GridSortSettings>
                    <GridColumns>
                        <GridColumn Field=@nameof(TimesheetEntry.RecordId) HeaderText="Record #" Width="80">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                    var recordDisplay = entry?.RecordId?.ToString() ?? "";
                                }
                                @recordDisplay
                            </Template>
                        </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.AttendanceDateTime) HeaderText="Date & Time" Width="150">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                    var dateTimeDisplay = entry?.AttendanceDateTime?.ToString("dd/MM/yyyy hh:mm tt") ?? "";
                                }
                                @dateTimeDisplay
                            </Template>
                        </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.IsPresent) HeaderText="Present" Width="80" Type="ColumnType.Boolean" TextAlign="TextAlign.Center">
                                <Template>
                                    @{
                                        var entry = (context as TimesheetEntry);
                                        var isPresent = entry?.IsPresent == true;
                                        var iconClass = isPresent ? "bi bi-check-circle-fill text-success" : "bi bi-x-circle-fill text-danger";
                                        var title = isPresent ? "Present" : "Absent";
                                    }
                                    <i class="@iconClass" title="@title" aria-label="@title"></i>
                                </Template>
                            </GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.Signature) HeaderText="Signature" Width="150"></GridColumn>
                        <GridColumn Field=@nameof(TimesheetEntry.Notes) HeaderText="Notes" Width="200"></GridColumn>
                        <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                            <Template>
                                @{
                                    var entry = (context as TimesheetEntry);
                                }
                                <div class="d-flex gap-1">
                                    <button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-half" @onclick="() => OpenEditEntryModal(entry)" title="Edit">
                                        <i class="bi bi-pencil" style="color: inherit;"></i>
                                        @if (ShowActionButtonLabel)
                                        {
                                            <span class="d-none d-lg-inline ms-1">Edit</span>
                                        }
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm grid-action-btn grid-btn-half" @onclick="() => DeleteTimesheetEntry(entry)" title="Delete" disabled="@(!CanDeleteTimesheetEntries)">
                                        <i class="bi bi-trash" style="color: inherit;"></i>
                                        @if (ShowActionButtonLabel)
                                        {
                                            <span class="d-none d-lg-inline ms-1">Delete</span>
                                        }
                                    </button>
                                </div>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }

            <div class="d-flex justify-content-end gap-2 mt-4">
@*                 <SfButton CssClass="btn btn-success" @onclick="ExportTimesheetToExcel">
                    <i class="bi bi-file-earmark-excel" style="color: white;"></i><span class="ms-2">Export to Excel</span>
                </SfButton> *@
                <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseViewModal">
                    <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Close</span>
                </SfButton>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Add/Edit Entry Modal -->
<SfDialog @bind-Visible="showEntryModal" Header="@entryModalTitle" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentEntry" OnValidSubmit="@SaveTimesheetEntry">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                    @if (isEditEntryMode)
                    {
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Record #</label>
                                <SfNumericTextBox TValue="int?" @bind-Value="currentEntry.RecordId"
                                                  Min="1" Max="999" Placeholder="Record number"></SfNumericTextBox>
                            </div>
                        </div>
                    }
                    <div class="row g-3"> 
                        <div class="col-md-8">
                            <label class="form-label">Date & Time</label>
                            <SfDateTimePicker TValue="DateTime?"
                                              @bind-Value="currentEntry.AttendanceDateTime"
                                              CssClass="form-control"
                                              Format="dd/MM/yyyy hh:mm tt"
                                              TimeFormat="hh:mm tt"
                                              Step="5"
                                              Placeholder="Leave empty if not attended">
                            </SfDateTimePicker>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Present</label>
                            <SfCheckBox @bind-Checked="currentEntry.IsPresent" Label="Student was present"></SfCheckBox>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Signature</label>
                            <SfTextBox @bind-Value="currentEntry.Signature" Placeholder="Signature or initials"></SfTextBox>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Notes</label>
                            <SfTextBox @bind-Value="currentEntry.Notes" Multiline="true" Placeholder="Additional notes"></SfTextBox>
                        </div>
                    </div>
                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSavingEntry">
                        @if (isSavingEntry)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditEntryMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditEntryMode ? "Update" : "Add")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseEntryModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Bulk Create Entries Modal -->
<SfDialog @bind-Visible="showBulkEntryModal" Header="Bulk Create Attendance Records" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <div class="row g-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        This will create multiple blank attendance records for this timesheet.
                        You can fill in the attendance details later by editing each record individually.
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label">Number of Records to Create *</label>
                    <SfNumericTextBox TValue="int" @bind-Value="numberOfRecords"
                                      Min="1" Max="50" Step="1"
                                      Placeholder="Enter number of records (1-50)"></SfNumericTextBox>
                    <div class="form-text">
                        Records will be created with sequential record numbers and empty attendance times.
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2 mt-4">
                <SfButton CssClass="btn btn-blue-custom" @onclick="CreateBulkTimesheetEntries" Disabled="@isSavingEntry">
                    @if (isSavingEntry)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    }
                    else
                    {
                        <i class="bi bi-plus-circle" style="color: white;"></i><span class="ms-2">Create</span>
                    }
                </SfButton>
                <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseBulkEntryModal">
                    <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                </SfButton>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Required Dialog Components -->
<DeleteConfirmationDialog />
<AlertDialog />
