@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@namespace ShiningCMusicApp.Components

<SfDialog @ref="dialogRef"
          Width="400px"
          Height="auto"
          IsModal="true"
          ShowCloseIcon="false"
          Visible="@isVisible"
          CssClass="recurring-event-action-dialog">
    <DialogTemplates>
        <Header>
            <div class="dialog-header">
                <i class="@GetHeaderIcon() me-2"></i>
                <span>@GetHeaderText()</span>
            </div>
        </Header>
        <Content>
            <div class="dialog-content">
                <p class="mb-3">@GetContentText()</p>

                <div class="action-options">
                    @if (currentActionType == ActionType.Edit)
                    {
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="editEvent"
                                   @onchange="() => selectedOption = ActionOption.EditEvent"
                                   checked="@(selectedOption == ActionOption.EditEvent)">
                            <label class="form-check-label" for="editEvent">
                                <strong>Edit Event</strong>
                                <small class="d-block text-muted">Edit only this occurrence</small>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="editFollowing"
                                   @onchange="() => selectedOption = ActionOption.EditFollowingEvents"
                                   checked="@(selectedOption == ActionOption.EditFollowingEvents)">
                            <label class="form-check-label" for="editFollowing">
                                <strong>Edit Following Events</strong>
                                <small class="d-block text-muted">Edit this and all future occurrences</small>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="editSeries"
                                   @onchange="() => selectedOption = ActionOption.EditSeries"
                                   checked="@(selectedOption == ActionOption.EditSeries)">
                            <label class="form-check-label" for="editSeries">
                                <strong>Edit Series</strong>
                                <small class="d-block text-muted">Edit the entire recurring series</small>
                            </label>
                        </div>
                    }
                    else
                    {
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="deleteEvent"
                                   @onchange="() => selectedOption = ActionOption.DeleteEvent"
                                   checked="@(selectedOption == ActionOption.DeleteEvent)">
                            <label class="form-check-label" for="deleteEvent">
                                <strong>Delete Event</strong>
                                <small class="d-block text-muted">Delete only this occurrence</small>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="deleteFollowing"
                                   @onchange="() => selectedOption = ActionOption.DeleteFollowingEvents"
                                   checked="@(selectedOption == ActionOption.DeleteFollowingEvents)">
                            <label class="form-check-label" for="deleteFollowing">
                                <strong>Delete Following Events</strong>
                                <small class="d-block text-muted">Delete this and all future occurrences</small>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="actionOption" id="deleteSeries"
                                   @onchange="() => selectedOption = ActionOption.DeleteSeries"
                                   checked="@(selectedOption == ActionOption.DeleteSeries)">
                            <label class="form-check-label" for="deleteSeries">
                                <strong>Delete Series</strong>
                                <small class="d-block text-muted">Delete the entire recurring series</small>
                            </label>
                        </div>
                    }
                </div>
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="btn btn-blue-custom" @onclick="OnConfirm">
                    <i class="bi bi-check-circle" style="color: white;"></i><span class="ms-2">OK</span>
                </SfButton>
                <SfButton CssClass="btn btn-cancel-custom" @onclick="OnCancel">
                    <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                </SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>
