using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/email")]
    [Authorize]
    public class EmailController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<EmailController> _logger;

        public EmailController(ApiClientService apiClient, ILogger<EmailController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpPost("send-schedule-ready/{tutorId}")]
        public async Task<IActionResult> SendScheduleReadyEmail(int tutorId)
        {
            try
            {
                _logger.LogInformation("Sending schedule ready email via API for tutor: {TutorId}", tutorId);
                
                var response = await _apiClient.PostAsync($"email/send-schedule-ready/{tutorId}", null);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    // Extract message from original API response and add success flag for BFF compatibility
                    var message = result.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : "Email sent successfully";
                    return Ok(new { success = true, message });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var errorProp) ? errorProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send schedule ready email. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return StatusCode(500, new { message = "Failed to send email" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending schedule ready email to tutor {TutorId} via API", tutorId);
                return StatusCode(500, new { message = "An error occurred sending email" });
            }
        }

        [HttpPost("send")]
        public async Task<IActionResult> SendEmail([FromBody] BffSendEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ToEmail))
                {
                    return BadRequest(new { message = "Recipient email is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Subject))
                {
                    return BadRequest(new { message = "Email subject is required" });
                }

                if (string.IsNullOrWhiteSpace(request.Body))
                {
                    return BadRequest(new { message = "Email body is required" });
                }

                _logger.LogInformation("Sending custom email via API to: {ToEmail}", request.ToEmail);
                
                // Convert BFF request to original API request format
                var apiRequest = new
                {
                    toEmail = request.ToEmail,
                    subject = request.Subject,
                    body = request.Body,
                    isHtml = request.IsHtml
                };
                
                var response = await _apiClient.PostAsync("email/send", apiRequest);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    // Extract message from original API response and add success flag for BFF compatibility
                    var message = result.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : $"Email sent to {request.ToEmail}";
                    return Ok(new { success = true, message });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var errorProp) ? errorProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send custom email. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return StatusCode(500, new { message = "Failed to send email" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending custom email to {ToEmail} via API", request.ToEmail);
                return StatusCode(500, new { message = "An error occurred sending email" });
            }
        }

        [HttpPost("send-template/{tutorId}")]
        public async Task<IActionResult> SendTemplateEmail(int tutorId, [FromBody] BffSendTemplateEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.TemplateName))
                {
                    return BadRequest(new { message = "Template name is required" });
                }

                _logger.LogInformation("Sending template email via API for tutor: {TutorId} with template: {TemplateName}", 
                    tutorId, request.TemplateName);
                
                // Convert BFF request to original API request format
                var apiRequest = new
                {
                    templateName = request.TemplateName
                };
                
                var response = await _apiClient.PostAsync($"email/send-template/{tutorId}", apiRequest);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    // Extract message from original API response and add success flag for BFF compatibility
                    var message = result.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : $"Email sent using template '{request.TemplateName}'";
                    return Ok(new { success = true, message });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Tutor not found" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var errorProp) ? errorProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send template email. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return StatusCode(500, new { message = "Failed to send email" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending template email to tutor {TutorId} with template {TemplateName} via API",
                    tutorId, request.TemplateName);
                return StatusCode(500, new { message = "An error occurred sending email" });
            }
        }

        [HttpPost("send-template-student/{studentId}")]
        public async Task<IActionResult> SendTemplateEmailToStudent(int studentId, [FromBody] BffSendTemplateEmailRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.TemplateName))
                {
                    return BadRequest(new { message = "Template name is required" });
                }

                _logger.LogInformation("Sending template email via API for student: {StudentId} with template: {TemplateName}",
                    studentId, request.TemplateName);

                // Convert BFF request to original API request format
                var apiRequest = new
                {
                    templateName = request.TemplateName,
                    paymentDeadlineDays = request.PaymentDeadlineDays
                };

                var response = await _apiClient.PostAsync($"email/send-template-student/{studentId}", apiRequest);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    // Extract message from original API response and add success flag for BFF compatibility
                    var message = result.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : $"Email sent using template '{request.TemplateName}'";
                    return Ok(new { success = true, message });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Student not found" });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var errorProp) ? errorProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }

                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send template email to student. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, failureContent);
                return StatusCode(500, new { message = "Failed to send email" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending template email to student {StudentId} with template {TemplateName} via API",
                    studentId, request.TemplateName);
                return StatusCode(500, new { message = "An error occurred sending email" });
            }
        }
    }

    public class BffSendEmailRequest
    {
        public string ToEmail { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = true;
    }

    public class BffSendTemplateEmailRequest
    {
        public string TemplateName { get; set; } = string.Empty;
        public int? PaymentDeadlineDays { get; set; }
    }
}
